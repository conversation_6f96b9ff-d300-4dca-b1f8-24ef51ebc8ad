<!DOCTYPE html>
<html>
<head>
    <title>Pro Ballers Player Lookup Helper</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .player-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .player-name { font-size: 18px; font-weight: bold; color: #2c3e50; }
        .search-btn { background: #3498db; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px 0; }
        .search-btn:hover { background: #2980b9; }
        .results { margin-top: 10px; }
        .player-result { border: 1px solid #27ae60; padding: 10px; margin: 5px 0; border-radius: 3px; cursor: pointer; }
        .player-result:hover { background: #ecf0f1; }
        .input-field { width: 100%; padding: 5px; margin: 5px 0; }
        .download-btn { background: #27ae60; color: white; padding: 15px 30px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; margin: 20px 0; }
        .status { font-weight: bold; }
        .found { color: #27ae60; }
        .not-found { color: #e74c3c; }
        .loading { color: #f39c12; }
    </style>
</head>
<body>
    <h1>Pro Ballers Player Headshot Lookup</h1>
    <p>This tool uses the Pro Ballers search_player API to find players and their headshots.</p>

    <div id="players-container"></div>

    <button class="download-btn" onclick="downloadCSV()">Download Updated CSV</button>

    <script>
        const players = ["Herkenhoff Philipp", "Hermannsson Martin", "Hinrichs Seth", "Hoellerl Nico"];
        let playerData = [];

        // Initialize player data
        players.forEach((name, index) => {
            playerData.push({
                name: name,
                playerUrl: '',
                headshotUrl: '',
                status: 'PENDING'
            });
        });

        function createPlayerCard(playerName, index) {
            return `
                <div class="player-card">
                    <div class="player-name">${playerName}</div>
                    <button class="search-btn" onclick="searchPlayer('${playerName}', ${index})">
                        Search Player
                    </button>
                    <div id="status-${index}" class="status">Ready to search</div>
                    <div id="results-${index}" class="results"></div>
                    <div style="margin-top: 10px;">
                        <input type="text" id="player-url-${index}" class="input-field"
                               placeholder="Player URL (will be filled automatically)" readonly>
                        <input type="text" id="headshot-url-${index}" class="input-field"
                               placeholder="Headshot URL (will be filled automatically)" readonly>
                    </div>
                </div>
            `;
        }

        function initializePage() {
            const container = document.getElementById('players-container');
            players.forEach((name, index) => {
                container.innerHTML += createPlayerCard(name, index);
            });
        }

        async function searchPlayer(playerName, index) {
            const statusDiv = document.getElementById(`status-${index}`);
            const resultsDiv = document.getElementById(`results-${index}`);
            const playerUrlInput = document.getElementById(`player-url-${index}`);
            const headshotUrlInput = document.getElementById(`headshot-url-${index}`);

            statusDiv.innerHTML = '<span class="loading">Searching...</span>';
            resultsDiv.innerHTML = '';

            try {
                // Try the search_player API
                const response = await fetch('https://www.proballers.com/search_player', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    },
                    body: JSON.stringify({ query: playerName })
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data && data.length > 0) {
                        statusDiv.innerHTML = '<span class="found">Players found! Click on a result:</span>';

                        data.slice(0, 5).forEach((player, playerIndex) => {
                            const playerDiv = document.createElement('div');
                            playerDiv.className = 'player-result';
                            playerDiv.innerHTML = `
                                <strong>${player.name || 'Unknown'}</strong><br>
                                <small>${player.team || 'No team info'}</small>
                            `;
                            playerDiv.onclick = () => selectPlayer(player, index);
                            resultsDiv.appendChild(playerDiv);
                        });
                    } else {
                        statusDiv.innerHTML = '<span class="not-found">No players found</span>';
                        playerData[index].status = 'NOT_FOUND';
                    }
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }

            } catch (error) {
                console.error('API Error:', error);
                statusDiv.innerHTML = '<span class="not-found">API blocked - Manual search required</span>';

                // Fallback to manual search URL
                const searchUrl = `https://www.proballers.com/search?q=${encodeURIComponent(playerName)}`;
                resultsDiv.innerHTML = `
                    <div style="margin-top: 10px;">
                        <a href="${searchUrl}" target="_blank" style="color: #3498db;">
                            Open manual search in new tab
                        </a>
                        <br><small>Copy player page URL and headshot URL manually</small>
                        <br>
                        <input type="text" placeholder="Paste player URL here"
                               onchange="updatePlayerUrl(${index}, this.value)" style="width: 100%; margin: 5px 0;">
                        <input type="text" placeholder="Paste headshot URL here"
                               onchange="updateHeadshotUrl(${index}, this.value)" style="width: 100%; margin: 5px 0;">
                    </div>
                `;
            }
        }

        async function selectPlayer(player, index) {
            const statusDiv = document.getElementById(`status-${index}`);
            const playerUrlInput = document.getElementById(`player-url-${index}`);
            const headshotUrlInput = document.getElementById(`headshot-url-${index}`);

            statusDiv.innerHTML = '<span class="loading">Fetching player page...</span>';

            // Construct player URL
            const playerUrl = player.url || `https://www.proballers.com/basketball/player/${player.id}/${player.slug}`;
            playerUrlInput.value = playerUrl;
            playerData[index].playerUrl = playerUrl;

            try {
                // Try to fetch the player page to get headshot
                const response = await fetch(playerUrl);
                const html = await response.text();

                // Look for headshot image in the HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const images = doc.querySelectorAll('img');

                let headshotUrl = '';
                for (let img of images) {
                    const src = img.src || img.getAttribute('src');
                    if (src && src.includes('proballers.com') &&
                        (src.includes('player') || src.includes('ul/player') || src.includes('media/cache'))) {
                        if (!src.includes('logo') && !src.includes('flag') && !src.includes('country')) {
                            headshotUrl = src;
                            break;
                        }
                    }
                }

                if (headshotUrl) {
                    headshotUrlInput.value = headshotUrl;
                    playerData[index].headshotUrl = headshotUrl;
                    playerData[index].status = 'FOUND';
                    statusDiv.innerHTML = '<span class="found">✓ Headshot found!</span>';
                } else {
                    playerData[index].status = 'NO_HEADSHOT';
                    statusDiv.innerHTML = '<span class="not-found">Player found but no headshot available</span>';
                }

            } catch (error) {
                console.error('Error fetching player page:', error);
                playerData[index].status = 'NO_HEADSHOT';
                statusDiv.innerHTML = '<span class="not-found">Player found but could not fetch headshot</span>';
            }
        }

        function updatePlayerUrl(index, url) {
            playerData[index].playerUrl = url;
            document.getElementById(`player-url-${index}`).value = url;
        }

        function updateHeadshotUrl(index, url) {
            playerData[index].headshotUrl = url;
            playerData[index].status = url ? 'FOUND' : 'PENDING';
            document.getElementById(`headshot-url-${index}`).value = url;
        }

        function downloadCSV() {
            let csv = "Player Name,Search URL,Player URL,Headshot URL,Status\n";

            playerData.forEach(player => {
                const searchUrl = `https://www.proballers.com/search?q=${encodeURIComponent(player.name)}`;
                csv += `"${player.name}","${searchUrl}","${player.playerUrl}","${player.headshotUrl}","${player.status}"\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'player_headshots_completed.csv';
            a.click();
            URL.revokeObjectURL(url);
        }

        // Initialize the page
        initializePage();
    </script>
</body>
</html>